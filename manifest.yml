modules:
  jira:customField:
    - key: simple-requirements-field
      name: Simple Requirements
      description: Input field for simple requirements for LLM expansion.
      type: string
      readOnly: false # Ensure it's editable
  function:
    - key: field-updated-handler # New function for the trigger
      handler: index.simpleRequirementsUpdatedHandler
    - key: requirements-processor
      handler: index.requirementsProcessorHandler
      timeoutSeconds: 900
  trigger: # New trigger module
    - key: field-updated-trigger
      function: field-updated-handler
      events:
        - avi:jira:updated:issue
  consumer:
    - key: requirements-consumer
      queue: requirements-queue
      resolver:
        function: requirements-processor
        method: requirementsProcessorHandler

app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/1855cc49-eb1c-46b8-b8d7-04e25dbd5aa8
permissions: # Added permissions section
  scopes:
    - read:jira-work # To read issue details and custom fields
    - write:jira-work # To update custom fields
  external:
    fetch:
      backend:
        - https://openrouter.ai # Allow calls to OpenRouter API
