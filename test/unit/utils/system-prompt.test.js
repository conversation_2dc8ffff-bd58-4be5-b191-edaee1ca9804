// Import the module under test
const { createFullPrompt, SYSTEM_PROMPT_TEMPLATE, RESPONSE_TEMPLATE } = await import('../../../build/system-prompt.js');

describe('system-prompt', () => {
  describe('SYSTEM_PROMPT_TEMPLATE', () => {
    it('should contain the expected template placeholders', () => {
      expect(SYSTEM_PROMPT_TEMPLATE).toContain('{{USER_INPUT}}');
      expect(SYSTEM_PROMPT_TEMPLATE).toContain('{{RESPONSE_TEMPLATE}}');
    });
  });

  describe('createFullPrompt', () => {
    const testUserInput = 'Create a user login system';

    it('should replace {{USER_INPUT}} placeholder with actual user input', () => {
      const result = createFullPrompt(testUserInput);
      
      expect(result).toContain(testUserInput);
      expect(result).not.toContain('{{USER_INPUT}}');
    });

    it('should replace {{RESPONSE_TEMPLATE}} placeholder with the actual template', () => {
      const result = createFullPrompt(testUserInput);
      
      expect(result).toContain('## Acceptance Criteria');
      expect(result).toContain('{{STORY_TITLE}}');
      expect(result).not.toContain('{{RESPONSE_TEMPLATE}}');
    });

    it('should preserve the core prompt structure after substitution', () => {
      const result = createFullPrompt(testUserInput);
      
      expect(result).toContain('requirements expansion assistant');
      expect(result).toContain(testUserInput);
    });

    it('should handle empty user input gracefully', () => {
      const result = createFullPrompt('');
      
      expect(result).toContain('## Acceptance Criteria');
      expect(result).not.toContain('{{USER_INPUT}}');
      expect(result).not.toContain('{{RESPONSE_TEMPLATE}}');
    });

    it('should handle special characters in user input', () => {
      const specialInput = 'Create login with "quotes" & symbols';
      const result = createFullPrompt(specialInput);
      
      expect(result).toContain(specialInput);
      expect(result).not.toContain('{{USER_INPUT}}');
    });

    it('should produce a complete system prompt with main placeholders replaced', () => {
      const result = createFullPrompt(testUserInput);
      
      // Ensure main template placeholders are replaced
      expect(result).not.toContain('{{USER_INPUT}}');
      expect(result).not.toContain('{{RESPONSE_TEMPLATE}}');
      
      // Ensure it contains expected content
      expect(result.length).toBeGreaterThan(500); // Should be a substantial prompt
    });
  });
});