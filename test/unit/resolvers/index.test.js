import { jest } from '@jest/globals';

// Create mock functions
const mockGetCustomFieldId = jest.fn();
const mockUpdateIssueFields = jest.fn();
const mockIsFieldUpdated = jest.fn();
const mockGetFieldChangeValue = jest.fn();
const mockValidateEnvironment = jest.fn();
const mockCallOpenRouter = jest.fn();
const mockMarkdownToAdf = jest.fn();
const mockCreateFullPrompt = jest.fn();
const mockQueuePush = jest.fn();

// Mock Queue class
const mockQueue = {
  push: mockQueuePush
};
const mockQueueConstructor = jest.fn().mockReturnValue(mockQueue);

// Mock modules using unstable_mockModule for ES modules
jest.unstable_mockModule('../../../src/utils/jira-helpers.js', () => ({
  getCustomFieldId: mockGetCustomFieldId,
  updateIssueFields: mockUpdateIssueFields,
  isFieldUpdated: mockIsFieldUpdated,
  getFieldChangeValue: mockGetFieldChangeValue,
  validateEnvironment: mockValidateEnvironment
}));

jest.unstable_mockModule('../../../src/utils/openrouter-client.js', () => ({
  callOpenRouter: mockCallOpenRouter
}));

jest.unstable_mockModule('marklassian', () => ({
  markdownToAdf: mockMarkdownToAdf
}));

jest.unstable_mockModule('@forge/events', () => ({
  Queue: mockQueueConstructor
}));

jest.unstable_mockModule('../../../build/system-prompt.js', () => ({
  createFullPrompt: mockCreateFullPrompt
}));

// Import the module under test after mocking
const { simpleRequirementsUpdatedHandler, requirementsProcessorHandler } = await import('../../../src/resolvers/index.js');

// Test data
const mockSimpleField = { id: 'customfield_10001', name: 'Simple Requirements' };
const mockFullField = { id: 'customfield_10002', name: 'Full Requirements' };
const mockEvent = {
  issue: { id: '10001', key: 'TEST-1' },
  changelog: {
    items: [{ fieldId: 'customfield_10001', field: 'Simple Requirements', from: 'old', to: 'new requirements' }]
  }
};
const mockEventNoChangelog = { issue: { id: '10001', key: 'TEST-1' } };
const mockContext = { accountId: 'test-account-id' };

describe('simpleRequirementsUpdatedHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockQueuePush.mockResolvedValue({ jobId: 'test-job-id' });
    // Reset the Queue constructor mock to return the mock queue
    mockQueueConstructor.mockReturnValue(mockQueue);
  });

  describe('Environment validation', () => {
    it('should return early if environment validation fails', async () => {
      mockValidateEnvironment.mockReturnValue({
        isValid: false,
        missing: ['SECRET_OPENROUTER_API_KEY']
      });

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockValidateEnvironment).toHaveBeenCalled();
      expect(mockGetCustomFieldId).not.toHaveBeenCalled();
    });

    it('should continue processing if environment validation passes', async () => {
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(false);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockValidateEnvironment).toHaveBeenCalled();
      expect(mockGetCustomFieldId).toHaveBeenCalledTimes(2);
    });
  });

  describe('Custom field resolution', () => {
    beforeEach(() => {
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
    });

    it('should return early if simple requirements field cannot be resolved', async () => {
      mockGetCustomFieldId.mockResolvedValueOnce(null);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetCustomFieldId).toHaveBeenCalledWith('simple-requirements-field');
      expect(mockIsFieldUpdated).not.toHaveBeenCalled();
    });

    it('should return early if full requirements field cannot be resolved', async () => {
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(null);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetCustomFieldId).toHaveBeenCalledWith('simple-requirements-field');
      expect(mockGetCustomFieldId).toHaveBeenCalledWith('full-requirements-field');
      expect(mockIsFieldUpdated).not.toHaveBeenCalled();
    });

    it('should proceed if both fields are resolved successfully', async () => {
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(false);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetCustomFieldId).toHaveBeenCalledWith('simple-requirements-field');
      expect(mockGetCustomFieldId).toHaveBeenCalledWith('full-requirements-field');
      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
    });
  });

  describe('Changelog validation', () => {
    beforeEach(() => {
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
    });

    it('should return early if no changelog is present', async () => {
      await simpleRequirementsUpdatedHandler(mockEventNoChangelog, mockContext);

      expect(mockIsFieldUpdated).not.toHaveBeenCalled();
    });

    it('should return early if simple requirements field was not updated', async () => {
      mockIsFieldUpdated.mockReturnValue(false);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockGetFieldChangeValue).not.toHaveBeenCalled();
    });

    it('should proceed if simple requirements field was updated', async () => {
      mockIsFieldUpdated.mockReturnValue(true);
      mockGetFieldChangeValue.mockReturnValue('');

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockGetFieldChangeValue).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
    });
  });

  describe('Requirements processing', () => {
    beforeEach(() => {
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockIsFieldUpdated.mockReturnValue(true);
    });

    it('should return early if simple requirements is empty', async () => {
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockGetFieldChangeValue.mockReturnValue('');

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetFieldChangeValue).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockQueuePush).not.toHaveBeenCalled();
    });

    it('should return early if simple requirements is whitespace only', async () => {
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockGetFieldChangeValue.mockReturnValue('   ');

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockQueuePush).not.toHaveBeenCalled();
    });

    it('should enqueue processing if requirements are provided', async () => {
      const simpleRequirements = 'Create user login functionality';
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(true);
      mockGetFieldChangeValue.mockReturnValue(simpleRequirements);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetFieldChangeValue).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockQueueConstructor).toHaveBeenCalledWith({ key: 'requirements-queue' });
      expect(mockQueuePush).toHaveBeenCalledWith([{
        body: {
          issueId: mockEvent.issue.id,
          issueKey: mockEvent.issue.key,
          simpleRequirements,
          simpleReqFieldId: mockSimpleField.id,
          fullReqFieldId: mockFullField.id
        }
      }]);
    });

    it('should handle queue errors gracefully', async () => {
      const simpleRequirements = 'Create user login functionality';
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(true);
      mockGetFieldChangeValue.mockReturnValue(simpleRequirements);
      mockQueuePush.mockRejectedValue(new Error('Queue failed'));

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockQueuePush).toHaveBeenCalled();
      // Should not throw - error is handled gracefully
    });
  });

  describe('Complete workflow', () => {
    it('should execute complete successful enqueue workflow', async () => {
      const simpleRequirements = 'Create user login functionality';
      
      // Setup all mocks for successful execution
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(true);
      mockGetFieldChangeValue.mockReturnValue(simpleRequirements);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      // Verify all steps were executed in correct order
      expect(mockValidateEnvironment).toHaveBeenCalled();
      expect(mockGetCustomFieldId).toHaveBeenCalledTimes(2);
      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockGetFieldChangeValue).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockQueueConstructor).toHaveBeenCalledWith({ key: 'requirements-queue' });
      expect(mockQueuePush).toHaveBeenCalledWith([{
        body: {
          issueId: mockEvent.issue.id,
          issueKey: mockEvent.issue.key,
          simpleRequirements,
          simpleReqFieldId: mockSimpleField.id,
          fullReqFieldId: mockFullField.id
        }
      }]);
    });
  });
});

describe('requirementsProcessorHandler', () => {
  const mockProcessorEvent = {
    call: {
      payload: {
        body: {
          issueId: '10001',
          issueKey: 'TEST-1',
          simpleRequirements: 'Create user login functionality',
          fullReqFieldId: 'customfield_10002'
        }
      }
    }
  };
  const mockContext = { accountId: 'test-account-id' };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AI Processing', () => {
    it('should create prompt and call OpenRouter API successfully', async () => {
      const mockPrompt = 'Full prompt content';
      const mockExpandedRequirements = '# Expanded Requirements\n\nDetailed requirements here.';
      const mockAdfContent = { type: 'doc', content: [] };

      mockCreateFullPrompt.mockReturnValue(mockPrompt);
      mockCallOpenRouter.mockResolvedValue(mockExpandedRequirements);
      mockMarkdownToAdf.mockReturnValue(mockAdfContent);
      mockUpdateIssueFields.mockResolvedValue(true);

      await requirementsProcessorHandler(mockProcessorEvent, mockContext);

      expect(mockCreateFullPrompt).toHaveBeenCalledWith('Create user login functionality');
      expect(mockCallOpenRouter).toHaveBeenCalledWith(mockPrompt);
      expect(mockMarkdownToAdf).toHaveBeenCalledWith(mockExpandedRequirements);
      expect(mockUpdateIssueFields).toHaveBeenCalledWith('10001', {
        'customfield_10002': mockAdfContent
      });
    });

    it('should throw error if OpenRouter API fails', async () => {
      const mockPrompt = 'Full prompt content';
      
      mockCreateFullPrompt.mockReturnValue(mockPrompt);
      mockCallOpenRouter.mockResolvedValue(null);

      await expect(requirementsProcessorHandler(mockProcessorEvent, mockContext))
        .rejects.toThrow('OpenRouter API failed to expand requirements');

      expect(mockMarkdownToAdf).not.toHaveBeenCalled();
      expect(mockUpdateIssueFields).not.toHaveBeenCalled();
    });

    it('should throw error if issue update fails', async () => {
      const mockPrompt = 'Full prompt content';
      const mockExpandedRequirements = '# Expanded Requirements\n\nDetailed requirements here.';
      const mockAdfContent = { type: 'doc', content: [] };

      mockCreateFullPrompt.mockReturnValue(mockPrompt);
      mockCallOpenRouter.mockResolvedValue(mockExpandedRequirements);
      mockMarkdownToAdf.mockReturnValue(mockAdfContent);
      mockUpdateIssueFields.mockResolvedValue(false);

      await expect(requirementsProcessorHandler(mockProcessorEvent, mockContext))
        .rejects.toThrow('Failed to update Jira issue with expanded requirements');
    });
  });
});