import { markdownToAdf } from 'marklassian';
import { getCustomFieldId, updateIssueFields, isFieldUpdated, getFieldChangeValue, validateEnvironment } from '../utils/jira-helpers.js';
import { callOpenRouter } from '../utils/openrouter-client.js';
import { Queue } from '@forge/events';
import { createFullPrompt } from '../../build/system-prompt.js';

// --- Handler for Issue Update Trigger (Optimized for Simple Requirements Field) ---

export const simpleRequirementsUpdatedHandler = async (event, context) => {
  console.log('Issue Updated Event Received:', JSON.stringify(event));
  console.log('Context:', JSON.stringify(context));

  const issueId = event.issue.id;
  const issueKey = event.issue.key;

  console.log(`Processing issue ${issueKey} (ID: ${issueId})`);

  // 1. Validate environment
  const envValidation = validateEnvironment();
  if (!envValidation.isValid) {
    console.error(`Missing required environment variables: ${envValidation.missing.join(', ')}`);
    console.error('Ensure OpenRouter API key was set using `forge variables set --encrypt SECRET_OPENROUTER_API_KEY`.');
    return;
  }

  // 2. Get Custom Field information using API (run in parallel for better performance)
  const [simpleReqField, fullReqField] = await Promise.all([
    getCustomFieldId('simple-requirements-field'),
    getCustomFieldId('full-requirements-field')
  ]);

  if (!simpleReqField) {
    console.error('Could not resolve Simple Requirements field information');
    return;
  }

  if (!fullReqField) {
    console.error('Could not resolve Full Requirements field information');
    return;
  }

  console.log(`Field IDs - Simple: ${simpleReqField.id}, Full: ${fullReqField.id}`);
  console.log(`Field Types - Simple: ${simpleReqField.schema?.type}, Full: ${fullReqField.schema?.type}`);

  // 3. Check if the Simple Requirements field was actually updated in this event
  const changelog = event.changelog;
  if (!changelog || !changelog.items) {
    console.log(`No changelog found in event. Skipping.`);
    return;
  }

  if (!isFieldUpdated(changelog, simpleReqField)) {
    console.log(`Simple Requirements field was not updated in this event. Skipping.`);
    return;
  }

  console.log(`Simple Requirements field was updated. Processing...`);

  // 4. Get the new value of the Simple Requirements field from the changelog
  const simpleRequirements = getFieldChangeValue(changelog, simpleReqField) || '';
  console.log(`Simple Requirements Input: ${simpleRequirements}`);

  if (!simpleRequirements || simpleRequirements.trim() === '') {
    console.log(`Simple Requirements field is empty for issue ${issueKey}. Skipping.`);
    return;
  }

  // 5. Enqueue the long-running processing to async event queue
  const queue = new Queue({ key: 'requirements-queue' });
  
  try {
    const { jobId } = await queue.push([{
      body: {
        issueId,
        issueKey,
        simpleRequirements,
        simpleReqFieldId: simpleReqField.id,
        fullReqFieldId: fullReqField.id
      }
    }]);
    
    console.log(`Successfully enqueued requirements processing for issue ${issueKey}. Job ID: ${jobId}`);
  } catch (error) {
    console.error(`Failed to enqueue requirements processing for issue ${issueKey}:`, error);
  }
};

// --- Async Event Consumer Handler for Long-Running AI Processing ---

export const requirementsProcessorHandler = async (event, context) => {
  console.log('Requirements Processor Event Received:', JSON.stringify(event));
  console.log('Context:', JSON.stringify(context));

  const { issueId, issueKey, simpleRequirements, fullReqFieldId } = event.call.payload.body;

  console.log(`Processing requirements for issue ${issueKey} (ID: ${issueId})`);
  console.log(`Simple Requirements Input: ${simpleRequirements}`);

  try {
    // 1. Create the fully formed prompt for AI processing
    console.log(`Creating prompt for AI processing...`);
    const prompt = createFullPrompt(simpleRequirements);

    // 2. Call OpenRouter API to expand requirements (with extended timeout)
    console.log(`Calling OpenRouter API to expand requirements...`);
    const fullRequirements = await callOpenRouter(prompt);
    
    if (!fullRequirements) {
      console.error('Failed to expand requirements using OpenRouter API');
      throw new Error('OpenRouter API failed to expand requirements');
    }

    console.log(`Successfully expanded requirements. Length: ${fullRequirements.length} characters`);

    // 3. Convert Markdown to ADF format
    console.log(`Converting Markdown to ADF format...`);
    const adfContent = markdownToAdf(fullRequirements);

    // 4. Update the issue with the expanded requirements
    console.log(`Updating issue ${issueKey} field ${fullReqFieldId} with ADF content...`);
    
    const updateSuccess = await updateIssueFields(issueId, {
      [fullReqFieldId]: adfContent
    });

    if (updateSuccess) {
      console.log(`Successfully updated 'Full Requirements' field (${fullReqFieldId}) for issue ${issueKey} with rich text formatted content.`);
    } else {
      console.error(`Failed to update issue ${issueKey} with expanded requirements.`);
      throw new Error('Failed to update Jira issue with expanded requirements');
    }

  } catch (error) {
    console.error(`Error processing requirements for issue ${issueKey}:`, error);
    
    // Let the error propagate to trigger retry mechanism
    throw error;
  }
};

