#!/usr/bin/env node

import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

function buildPrompts() {
  try {
    // Read the system prompt and response template markdown files
    const promptPath = join(projectRoot, 'resources/system-prompt.md');
    const templatePath = join(projectRoot, 'resources/response-template.md');
    
    const promptContent = readFileSync(promptPath, 'utf-8');
    const templateContent = readFileSync(templatePath, 'utf-8');
    
    // Extract the actual prompt content, removing markdown headers
    const cleanPrompt = promptContent
      .replace(/# System Prompt for AI Requirements Expansion\n\n/, '')
      .replace(/## Task\n\n/, '')
      .replace(/## Guidelines\n\n/, '')
      .replace(/## Template Structure\nUse this structure for your response:\n\n/, '')
      .replace(/- /g, '• ')
      .trim();
    
    // Generate the JavaScript module
    const jsContent = `/**
 * System Prompt for AI Requirements Expansion
 * 
 * This file is auto-generated from system-prompt.md and response-template.md during build.
 * Do not edit this file directly - edit the source markdown files instead.
 */

/**
 * The system prompt template that defines the AI assistant's role and behavior
 * @type {string}
 */
export const SYSTEM_PROMPT_TEMPLATE = ${JSON.stringify(cleanPrompt, null, 2)};

/**
 * The response template structure for the AI to follow
 * @type {string}
 */
export const RESPONSE_TEMPLATE = ${JSON.stringify(templateContent, null, 2)};

/**
 * Create a complete system prompt by substituting template variables
 * @param {string} userInput - The user's input requirements
 * @returns {string} The complete system prompt with substituted values
 */
export function createFullPrompt(userInput) {
  return SYSTEM_PROMPT_TEMPLATE
    .replace('{{USER_INPUT}}', userInput)
    .replace('{{RESPONSE_TEMPLATE}}', RESPONSE_TEMPLATE);
}
`;
    
    // Ensure build directory exists
    const buildDir = join(projectRoot, 'build');
    if (!existsSync(buildDir)) {
      mkdirSync(buildDir, { recursive: true });
    }
    
    // Write the generated module
    const outputPath = join(buildDir, 'system-prompt.js');
    writeFileSync(outputPath, jsContent);
    
    console.log('✅ Successfully generated build/system-prompt.js from system-prompt.md');
    
  } catch (error) {
    console.error('❌ Failed to build prompts:', error);
    process.exit(1);
  }
}

buildPrompts();